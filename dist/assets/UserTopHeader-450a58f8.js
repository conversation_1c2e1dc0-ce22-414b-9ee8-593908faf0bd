import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as x,r as d,u as b,L as v,k as p}from"./vendor-1c28ea83.js";import{G as y,A as N,a0 as j,aa as w,L as _}from"./index-bdd93324.js";import{B as g}from"./index-12048740.js";import"./pdf-lib-623decea.js";import{b as c}from"./index.esm-1ac45320.js";import{P as A}from"./index-30208b96.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const L={manage_orders:{access:"restricted-access",allow:!1},manage_receipts:{access:"restricted-access",allow:!1},manage_inventory:{access:"restricted-access",allow:!1},view_address_book:{access:"restricted-access",allow:!1},manage_exhibitor_requests:{access:"restricted-access",allow:!1}},r={LINK:"link",DROPDOWN:"dropdown"},I=[{to:"/member/new-order",text:"New Order",type:r.LINK,value:"new order"},{to:"/member/pending-orders",text:"Pending Orders",type:r.LINK,value:"pending orders"},{to:"/member/cancelled-orders",text:"Cancelled Orders",type:r.LINK,value:"cancelled orders"},{to:"/member/completed-orders",text:"Completed Orders",type:r.LINK,value:"completed orders"}],k=[{to:"/member/inventory",text:"View Inventory",type:r.LINK,value:"view inventory"},{to:"/member/purge-requests",text:"Purge Requests",type:r.LINK,value:"purge requests"},{to:"/member/hold-requests",text:"Hold Requests",type:r.LINK,value:"hold requests"},{to:"/member/cylce-count-requests",text:"Cylce Count Requests",type:r.LINK,value:"cylce count requests"}],K=[{to:"/member/dashboard",text:"Dashboard",type:r.LINK,icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),value:"dashboard",access:"open"},{to:"/member/receipts",text:"Receipts",type:r.LINK,icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),value:"receipts",access:"restricted",permission_accessor:"manage_receipts"},{to:"/member/inventory",text:"Inventory",type:r.LINK,dropdownItems:k,icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),value:"inventory",access:"restricted",permission_accessor:"manage_inventory"},{to:"/member/orders?view=pending",text:"Orders",type:r.LINK,dropdownItems:I,icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),value:"orders",access:"restricted",permission_accessor:"manage_orders"},{to:"/member/products",text:"Products",type:r.LINK,value:"products",icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),access:"open"},{to:"/member/installation",text:"Installation",type:r.LINK,icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),value:"installation",access:"open"},{to:"/member/exhibitor_request?view=pending",text:"Exhibitor Requests",type:r.LINK,value:"exhibitor request",icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),access:"restricted",permission_accessor:"manage_exhibitor_requests"},{to:"/member/employee",text:"Employees",type:r.LINK,value:"employees",icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),access:"restricted",permission_accessor:"manage_employees"},{to:"/member/address_book",text:"Address Book",value:"address_book",type:r.LINK,icon:e.jsx(c,{className:"text-xl text-[#A8A8A8]"}),access:"restricted",permission_accessor:"view_address_book"}],ae=()=>{const{state:u,dispatch:q}=x.useContext(y);x.useContext(N);const[O,n]=d.useState(""),{isOpen:R,showBackButton:f,path:o}=u,i=b(),{profile:t}=j(),m=s=>{if(t!=null&&t.is_company||t!=null&&t.company_permission&&["full-access"].includes(t==null?void 0:t.company_permission)&&!["manage_employees"].includes(s.permission_accessor))return!0;if(t!=null&&t.company_permission&&["restricted-access"].includes(t==null?void 0:t.company_permission)&&!["manage_employees"].includes(s.permission_accessor)){const a=(t!=null&&t.company_roles&&JSON.parse(t==null?void 0:t.company_roles)?JSON.parse(t==null?void 0:t.company_roles):L)[s==null?void 0:s.permission_accessor];return!!(a!=null&&a.allow)}};return d.useEffect(()=>{const s=i.pathname.split("/");s[1]!=="user"&&s[1]!=="user"?n(s[1]):n(s[2])},[i]),e.jsxs("div",{className:"sticky inset-x-0 top-0 z-20 m-auto flex h-fit max-h-fit min-h-[5.4375rem] w-full min-w-full max-w-full flex-col items-center justify-between bg-black px-6  pt-2 md:min-w-[auto] md:max-w-[auto]",children:[e.jsxs("div",{className:"flex w-full min-w-full max-w-full justify-between gap-10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[f&&e.jsx(g,{}),e.jsx("h1",{className:"text-xl capitalize",children:e.jsx(w,{})})]}),e.jsx(_,{children:e.jsx(A,{})})]}),e.jsx("div",{className:"scrollbar-hide w-full max-w-full overflow-x-auto md:overflow-x-clip",children:e.jsx("ul",{className:"flex w-fit justify-start text-sm",children:K.map(s=>{var l;switch(s.type){case r.LINK:if(["open"].includes(s.access))return e.jsx("li",{className:`flex !w-fit !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2  ${o===s.value?"border-b-2 border-b-third text-white":"text-[#ffffff99]"}`,children:e.jsx(p,{to:s.to,className:`${o===s.value?"active-nav":""} !w-fit`,children:e.jsx("div",{className:"flex !w-fit items-center gap-3",children:e.jsx("span",{className:"!w-fit",children:s.text})})})},s.value);if(["restricted"].includes(s.access)&&m(s))return e.jsx("li",{className:`flex !w-fit !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2  ${o===s.value?"border-b-2 border-b-third text-white":"text-[#ffffff99]"}`,children:e.jsx(p,{to:s.to,className:`${o===s.value?"active-nav":""} !w-fit`,children:e.jsx("div",{className:"flex !w-fit items-center gap-3",children:e.jsx("span",{className:"!w-fit",children:s.text})})})},s.value);if(["restricted"].includes(s.access)&&!m(s))return null;case r.DROPDOWN:return e.jsx(e.Fragment,{children:e.jsxs("li",{className:` relative flex cursor-pointer list-none items-center justify-center px-2 ${o===s.value?"border-b-2 border-b-third text-white":"text[#ffffff99"}`,children:[e.jsxs("button",{className:"peer flex h-fit cursor-pointer items-center gap-2 text-[1rem] ",children:[s.text,e.jsx("span",{className:"text-xs",children:"▽"})]}),e.jsx("ul",{className:"absolute top-[80%] z-20 hidden min-w-[12.5rem] rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-white shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block",children:s!=null&&s.dropdownItems&&((l=s.dropdownItems)!=null&&l.length)?s.dropdownItems.map((a,h)=>e.jsx("li",{className:"!w-fit",children:e.jsx(v,{className:`hover:text[#262626] flex !w-fit cursor-pointer items-center rounded-md px-4 py-3 text-black hover:bg-[#F4F4F4] ${o===a.value?"active-nav":""}`,to:a==null?void 0:a.to,children:e.jsx("span",{children:a.text})})},h)):null})]})})}})})})]})};export{ae as default};
