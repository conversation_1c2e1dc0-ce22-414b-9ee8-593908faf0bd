import{j as r}from"./@react-google-maps/api-211df1ae.js";import{T as a}from"./index-3bf596a0.js";import{r as p}from"./vendor-1c28ea83.js";import{_ as c}from"./qr-scanner-cf010ec4.js";import{L as m}from"./index-bdd93324.js";import{A as x}from"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const f=p.lazy(()=>c(()=>import("./CircularImagePreview-45d0d9b2.js"),["assets/CircularImagePreview-45d0d9b2.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-bdd93324.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-1b5612ec.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-d6d1c22a.js","assets/@react-pdf-viewer/core-75a73120.js","assets/react-calendar-eace60fa.js","assets/index-b57b4504.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-e2604cb4.js","assets/index-aed072b8.js"])),J=({image:l="",className:d="",title:t="Image Upload",onUpload:n=null,name:s="photo",multiple:i=!1})=>{const e=p.useRef();return r.jsxs("div",{className:`grid w-full grid-cols-1 gap-[1.5rem] ${d}`,children:[r.jsx(a,{className:"!border-0 !p-0 !shadow-none ",children:t}),r.jsxs("div",{className:"flex w-full items-start justify-between gap-5 ",children:[r.jsx("div",{className:"p-2",children:r.jsx(m,{children:r.jsx(f,{image:l,className:"h-[5rem] w-[5rem]"})})}),r.jsxs("div",{className:"grid grow grid-cols-1 gap-2",children:[r.jsx(a,{className:"!border-0 !p-0 !shadow-none ",children:t}),r.jsx("p",{className:"font-inter text-[.875rem] font-[400] leading-[1.25rem]  tracking-[-0.6%] text-sub-500",children:"Min 400x400px, PNG or JPEG"}),r.jsx(m,{children:r.jsxs(x,{className:"!shadow-0 !w-fit !border-gray-200 !bg-white font-[700] !text-sub-500",showPlus:!1,onClick:()=>{var o;(o=e==null?void 0:e.current)==null||o.click()},children:[r.jsx("input",{hidden:!0,ref:e,type:"file",id:s,name:s,accept:".jpg,.jpeg,.png",multiple:i,style:{display:"none"},onChange:o=>{n(s,o.target,i),e.current.value=""}}),"Upload"]})})]})]})]})};export{J as default};
