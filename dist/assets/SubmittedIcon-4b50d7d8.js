import{j as C}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const o=({className:s="",fill:t="#7B1113",stroke:a="#717179",onClick:x=()=>{}})=>C.jsxs("svg",{className:`${s}`,onClick:x,width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{d:"M6.3335 0.333374C6.60964 0.333374 6.8335 0.557232 6.8335 0.833374V2.00004C6.8335 2.27618 6.60964 2.50004 6.3335 2.50004C6.05735 2.50004 5.8335 2.27618 5.8335 2.00004V0.833374C5.8335 0.557232 6.05735 0.333374 6.3335 0.333374Z",fill:t}),C.jsx("path",{d:"M10.6245 2.04175C10.8198 2.23701 10.8198 2.55359 10.6245 2.74885L9.75078 3.62263C9.55551 3.81789 9.23893 3.81789 9.04367 3.62263C8.84841 3.42736 8.84841 3.11078 9.04367 2.91552L9.91744 2.04175C10.1127 1.84648 10.4293 1.84648 10.6245 2.04175Z",fill:t}),C.jsx("path",{d:"M3.62325 9.7508C3.81851 9.55554 3.81851 9.23896 3.62325 9.0437C3.42799 8.84844 3.1114 8.84844 2.91614 9.0437L2.04237 9.91747C1.84711 10.1127 1.84711 10.4293 2.04237 10.6246C2.23763 10.8198 2.55421 10.8198 2.74947 10.6246L3.62325 9.7508Z",fill:t}),C.jsx("path",{d:"M2.50016 6.33337C2.50016 6.60952 2.27631 6.83337 2.00016 6.83337H0.833496C0.557354 6.83337 0.333496 6.60952 0.333496 6.33337C0.333496 6.05723 0.557354 5.83337 0.833496 5.83337H2.00016C2.27631 5.83337 2.50016 6.05723 2.50016 6.33337Z",fill:t}),C.jsx("path",{d:"M2.91607 3.62313C3.11133 3.81839 3.42791 3.81839 3.62317 3.62313C3.81843 3.42786 3.81843 3.11128 3.62317 2.91602L2.7494 2.04225C2.55414 1.84698 2.23755 1.84698 2.04229 2.04225C1.84703 2.23751 1.84703 2.55409 2.04229 2.74935L2.91607 3.62313Z",fill:t}),C.jsx("path",{d:"M6.36779 5.34167C5.83139 5.12712 5.2946 5.64987 5.49486 6.19176L8.07238 13.1662C8.26994 13.7008 9.00371 13.7571 9.28048 13.2589L10.7015 10.701L13.2702 9.27394C13.7612 9.00118 13.7155 8.28076 13.1941 8.07218L6.36779 5.34167Z",fill:t})]});export{o as default};
