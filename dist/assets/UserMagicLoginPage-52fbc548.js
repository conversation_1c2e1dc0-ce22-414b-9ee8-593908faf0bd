import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as a,f as b,d as f}from"./vendor-1c28ea83.js";import{u as w}from"./react-hook-form-eec8b32f.js";import{A as j,G as y,o as N,M as v,s as S}from"./index-bdd93324.js";import{c as A,a as k}from"./yup-1b5612ec.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const V=()=>{var i,m;const l=A({email:k().email().required()}).required();a.useContext(j);const{dispatch:n}=a.useContext(y),[c,s]=a.useState(!1),o=b();f();const{register:p,handleSubmit:u,setError:d,formState:{errors:r}}=w({resolver:N(l)}),x=async g=>{let h=new v;try{s(!0);const t=await h.magicLoginAttempt(g.email,o==null?void 0:o.role);t.error||(s(!1),console.log(t),S(n,"Please check your mail to complete login attempt"))}catch(t){s(!1),console.log("Error",t),d("email",{type:"manual",message:t.message})}};return e.jsxs("div",{className:"w-full max-w-xs mx-auto",children:[e.jsxs("form",{onSubmit:u(x),className:"bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4 mt-8 ",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-gray-700 text-sm font-bold mb-2",htmlFor:"email",children:"Email"}),e.jsx("input",{type:"email",placeholder:"Email",...p("email"),className:`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline ${(i=r.email)!=null&&i.message?"border-red-500":""}`}),e.jsx("p",{className:"text-red-500 text-xs italic",children:(m=r.email)==null?void 0:m.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("button",{type:"submit",className:"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline",children:[c?"Attempting Log In...":"Sign In"," "]})})]}),e.jsxs("p",{className:"text-center text-gray-500 text-xs",children:["© ",new Date().getFullYear()," manaknightdigital inc. All rights reserved."]})]})};export{V as default};
