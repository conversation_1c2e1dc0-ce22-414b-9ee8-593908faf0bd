import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as f}from"./vendor-1c28ea83.js";import u from"./MkdPopover-c3c82be8.js";import{az as w,aA as j,aB as i,aC as o,aj as C,aD as g,aE as A}from"./index-bdd93324.js";import"./react-tooltip-ad20e3af.js";import"./@mantine/core-76ed7ee7.js";import"./@uppy/dashboard-3a4b1704.js";import"./@craftjs/core-a5d68af1.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const b={damaged:{icon:e.jsx(w,{}),text:"damaged",details:"The item is damaged and needs attention."},"place order":{icon:e.jsx(j,{}),text:"Place Order",details:"The order has been placed and is awaiting processing."},verified:{icon:e.jsx(i,{}),text:"Verified",details:"The address has been verified against the database."},unverified:{icon:e.jsx(i,{fill:"#A1A1A9"}),text:"unverified",details:"The address validation failed during pre-validation."},warning:{icon:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 13C11.45 13 11 12.55 11 12V8C11 7.45 11.45 7 12 7C12.55 7 13 7.45 13 8V12C13 12.55 12.55 13 12 13ZM13 17H11V15H13V17Z",fill:"#F59E0B"})}),text:"warning",details:"The address validation succeeded, but it should be double-checked."},error:{icon:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.4902 2.84406C11.1661 1.69 12.8343 1.69 13.5103 2.84406L22.0156 17.3654C22.699 18.5321 21.8576 19.9999 20.5056 19.9999H3.49483C2.14281 19.9999 1.30147 18.5321 1.98479 17.3654L10.4902 2.84406ZM12 9C12.4142 9 12.75 9.33579 12.75 9.75V13.25C12.75 13.6642 12.4142 14 12 14C11.5858 14 11.25 13.6642 11.25 13.25V9.75C11.25 9.33579 11.5858 9 12 9ZM13 15.75C13 16.3023 12.5523 16.75 12 16.75C11.4477 16.75 11 16.3023 11 15.75C11 15.1977 11.4477 14.75 12 14.75C12.5523 14.75 13 15.1977 13 15.75Z",fill:"#DC2626"})}),text:"error",details:"The address validation failed with no certainty."},submitted:{icon:e.jsx(j,{}),text:"submitted",details:"The data has been submitted."},processing:{icon:e.jsx(o,{}),text:"Processing",details:"The data is currently being processed."},label_created:{icon:e.jsx(o,{}),text:"Label Created",details:"A shipping label has been created for the order."},pending:{icon:e.jsx(o,{}),text:"pending",details:"The process is pending and awaiting further action."},completed:{icon:e.jsx(i,{}),text:"completed",details:"The process has been completed successfully."},cancelled:{icon:e.jsx(C,{fill:"red",stroke:"white"}),text:"cancelled",details:"The process has been cancelled."},voided:{icon:e.jsx(g,{}),text:"Voided",details:"The order has been voided."},deleted:{icon:e.jsx(C,{fill:"red",stroke:"white"}),text:"deleted",details:"The data has been deleted."},false:{icon:e.jsx(i,{fill:"#A1A1A9"}),text:"false",details:"The status is false."},active:{icon:e.jsx(i,{}),text:"Active",details:"The status is active."},inactive:{icon:e.jsx(i,{fill:"#A1A1A9"}),text:"Inactive",details:"The status is inactive."},save:{icon:e.jsx(i,{}),text:"",details:"The status is saved."},no_save:{icon:e.jsx(i,{fill:"#A1A1A9"}),text:"",details:"The status is not saved."},closed:{icon:e.jsx(i,{}),text:"Closed",details:"The status is closed."},open:{icon:e.jsx(i,{fill:"#A1A1A9"}),text:"Open",details:"The status is open."},true:{icon:e.jsx(i,{}),text:"true",details:"The status is true."},"not held":{icon:e.jsx(i,{}),text:"Not Held",details:"The inventory is not held."},held:{icon:e.jsx(i,{fill:"#A1A1A9"}),text:"Held",details:"The inventory is held."},approved:{icon:e.jsx(i,{}),text:"approved",details:"The status is approved."},charged:{icon:e.jsx(A,{}),text:"Charge Added",details:"A charge has been added."}},T=({value:s,mappings:r})=>{var a,l,n,c,x,h,p,m;const t=["object"].includes(typeof r[s])&&!Array.isArray(r[s])?b[(l=(a=r[s])==null?void 0:a.text)==null?void 0:l.toLowerCase()]:["string","number"].includes(typeof r[s])?b[(n=r[s])==null?void 0:n.toLowerCase()]:null,d=["object"].includes(typeof r[s])&&!Array.isArray(r[s])?(x=(c=r[s])==null?void 0:c.text)==null?void 0:x.toLowerCase():["string","number"].includes(typeof r[s])?(h=r[s])==null?void 0:h.toLowerCase():null;return e.jsx(f.Fragment,{children:t!=null&&t.text||d?e.jsx(u,{display:e.jsxs("span",{style:{backgroundColor:(p=r[s])==null?void 0:p.bg,color:(m=r[s])==null?void 0:m.color},className:"flex w-fit items-center justify-normal gap-[.25rem] rounded-[.375rem] border border-gray-200 p-[.25rem_.5rem_.25rem_.25rem] capitalize",children:[t!=null&&t.icon?e.jsx("span",{className:"cursor-pointer",children:t==null?void 0:t.icon}):null,e.jsx("span",{className:"max-w-[9.375rem] truncate",children:(t==null?void 0:t.text)??d})]}),place:"left",backgroundColor:"#000",openOnClick:!1,children:e.jsx("div",{className:"flex max-w-[9.375rem] items-center gap-2",children:e.jsx("span",{className:"w-full whitespace-normal break-words text-white",children:(t==null?void 0:t.details)??(t==null?void 0:t.text)??d})})}):null})},W=f.memo(T);export{W as default,b as mappingValues};
