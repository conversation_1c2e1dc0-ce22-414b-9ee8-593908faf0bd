import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as P,R as g}from"./vendor-1c28ea83.js";import{u as z}from"./react-hook-form-eec8b32f.js";import{M,G as S,A as $,a0 as D,s as b,t as I,a as O,o as R}from"./index-bdd93324.js";import{c as F,a as j,b as U}from"./yup-1b5612ec.js";import{I as q}from"./index-632d14e3.js";import{M as B}from"./index-aed072b8.js";import"./react-toggle-58b0879a.js";/* empty css                 */import{A as G}from"./index-e2604cb4.js";import{_ as T}from"./qr-scanner-cf010ec4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./@uppy/dashboard-3a4b1704.js";const v=P.lazy(()=>T(()=>import("./MkdPasswordInput-420f1f4f.js"),["assets/MkdPasswordInput-420f1f4f.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/MkdInput-8c5a964a.js","assets/index-bdd93324.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-1b5612ec.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-d6d1c22a.js","assets/@react-pdf-viewer/core-75a73120.js","assets/react-calendar-eace60fa.js","assets/index-b57b4504.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/react-toggle-58b0879a.js","assets/@uppy/dashboard-3a4b1704.js","assets/MkdInput-3e37c863.css","assets/index.esm-2d1feecf.js"])),be=({isOpen:l=!1,onClose:r=null})=>{var d,c;let C=new M;const{dispatch:m}=g.useContext(S),{dispatch:k}=g.useContext($);D();const N=F({password:j().required().min(8).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,{message:"Password must be at least 8 characters long, Contain at least one uppercase letter, One lowercase letter, One number, one number and one special character."}),confirm:j().oneOf([U("password")],"Passwords do not match")}).required(),{register:n,handleSubmit:y,setError:E,setValue:V,watch:Z,formState:{errors:s}}=z({resolver:R(N)}),A=P.useCallback(async a=>{var p,u,f,x,w;try{if(a!=null&&a.password&&((p=a==null?void 0:a.password)==null?void 0:p.length)>0){const t=await C.updatePassword(a.password);if(!t.error)b(m,"Password Updated",5e3,"success");else if(t.validation){const o=Object.keys(t.validation);for(let i=0;i<o.length;i++){const h=o[i];E(h,{type:"manual",message:t.validation[h]})}}}}catch(t){const o=(f=(u=t==null?void 0:t.response)==null?void 0:u.data)!=null&&f.message?(w=(x=t==null?void 0:t.response)==null?void 0:x.data)==null?void 0:w.message:t==null?void 0:t.message;b(m,o,5e3,"error"),I(k,o)}},[]);return e.jsx(e.Fragment,{children:e.jsx(B,{isOpen:l,modalCloseClick:()=>r&&r(),title:"Edit Password",modalHeader:!0,classes:{modalDialog:"!grid grid-rows-[auto_90%] !gap-0 !w-full !px-0 md:!w-[25.125rem] !h-fit grid-rows-[auto_auto]",modalContent:"!z-10 !px-0 overflow-hidden !pt-0 !mt-0",modal:"h-full"},children:e.jsx("div",{className:"h-full min-h-full p-5",children:l?e.jsx(e.Fragment,{children:e.jsxs("form",{className:"relative mx-auto grid h-full max-h-full min-h-full w-full grow grid-cols-1 grid-rows-[75%_20%] gap-5 rounded text-start !font-inter leading-snug tracking-wide",onSubmit:y(A),children:[e.jsxs("div",{className:"flex w-full flex-col gap-5",children:[e.jsxs("div",{children:[e.jsx(v,{name:"password",label:"Password",className:"grow",register:n}),s&&(s==null?void 0:s.password)&&e.jsx("p",{className:"text-field-error m-auto mt-2 text-[.8rem] italic text-red-500",children:(d=s==null?void 0:s.password)==null?void 0:d.message})]}),e.jsxs("div",{children:[e.jsx(v,{name:"confirm",label:"Confirm Password",className:"grow",register:n}),s&&(s==null?void 0:s.confirm)&&e.jsx("p",{className:"text-field-error m-auto mt-2 text-[.8rem] italic text-red-500",children:O((c=s==null?void 0:s.confirm)==null?void 0:c.message,{casetype:"capitalize",separator:" "})})]})]}),e.jsxs("div",{className:"relative flex w-full gap-5",children:[e.jsx(G,{onClick:()=>r(),className:"!grow !border-none !bg-soft-200 !text-sub-500",children:"Cancel"}),e.jsx(q,{type:"submit",className:"!grow px-4 py-2 font-bold capitalize text-white",children:"Update Password"})]})]})}):null})})})};export{be as default};
