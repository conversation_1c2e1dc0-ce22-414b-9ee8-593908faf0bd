import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as l}from"./vendor-1c28ea83.js";import{C as t,T as a}from"./index-3bf596a0.js";import"./qr-scanner-cf010ec4.js";const p=()=>{const s=l(),r=[{id:"notifications",title:"Notifications",description:"Manage your notification preferences"},{id:"privacy",title:"Privacy",description:"Control your privacy settings"},{id:"security",title:"Security",description:"Update your security settings"},{id:"billing",title:"Billing",description:"Manage your payment methods and billing history"}];return e.jsxs("div",{className:"space-y-6 p-4 md:p-6",children:[e.jsx(t,{children:e.jsx(a,{children:"Profile Settings"})}),e.jsx("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:r.map(i=>e.jsx(t,{children:e.jsxs("button",{onClick:()=>s(`/member/profile/settings/${i.id}`),className:"flex w-full flex-col gap-2 text-left",children:[e.jsx("h3",{className:"text-xl font-bold text-[#eaeaea]",children:i.title}),e.jsx("p",{className:"text-[#b5b5b5]",children:i.description})]})},i.id))}),e.jsx(t,{children:e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>s("/member/profile/edit"),className:"rounded-lg bg-[#2e7d32] px-6 py-3 text-[#eaeaea]",children:"Edit Profile"})})})]})};export{p as default};
