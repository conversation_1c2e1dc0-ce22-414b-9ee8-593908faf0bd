import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as s}from"./vendor-1c28ea83.js";import{_ as r}from"./qr-scanner-cf010ec4.js";const l=s.lazy(()=>r(()=>import("./Sidebar-d91905eb.js"),["assets/Sidebar-d91905eb.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-bdd93324.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-1b5612ec.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-d6d1c22a.js","assets/@react-pdf-viewer/core-75a73120.js","assets/react-calendar-eace60fa.js","assets/index-b57b4504.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),o=s.lazy(()=>r(()=>import("./Header-f3b719e0.js"),["assets/Header-f3b719e0.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"])),m=({children:a})=>e.jsxs("div",{className:"flex min-h-screen no-scrollbar flex-col bg-[#1e1e1e]",children:[e.jsx(o,{}),e.jsxs("div",{className:"flex flex-1",children:[e.jsx("div",{className:"w-64 bg-[#161616]",children:e.jsx(l,{})}),e.jsx("div",{style:{scrollbarWidth:"none"},className:"flex-1 overflow-auto",children:e.jsx("main",{className:"min-h-[calc(100vh-62px)]",children:a})})]})]});export{m as UserWrapper,m as default};
