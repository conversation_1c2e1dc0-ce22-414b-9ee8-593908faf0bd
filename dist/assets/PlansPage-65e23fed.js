import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as a}from"./vendor-1c28ea83.js";import{G as N,M as p,s as n,T as C}from"./index-bdd93324.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const R=()=>{const{dispatch:r}=a.useContext(N),[h,u]=a.useState([]),[j,i]=a.useState(!0),[d,o]=a.useState(""),[b,c]=a.useState(!1),[l,g]=a.useState(null);a.useEffect(()=>{x()},[]);const x=async()=>{try{i(!0);const t=await new p().GetUserSubscriptions();t.error?(o(t.message||"Failed to load subscriptions"),n(r,"error",t.message||"Failed to load subscriptions")):u(t.list)}catch(s){o(s.message),n(r,"error",s.message)}finally{i(!1)}},f=s=>{g(s),c(!0)},k=async()=>{try{i(!0);const t=await new p().CancelSubscription({subscription_id:l.id.value,reason:"User requested cancellation"});t.error?(o(t.message||"Failed to cancel subscription"),n(r,"error",t.message||"Failed to cancel subscription")):(n(r,"success","Subscription cancelled successfully"),c(!1),x())}catch(s){o(s.message),n(r,"error",s.message)}finally{i(!1)}},m=s=>new Date(s).toLocaleDateString("en-US",{month:"long",day:"numeric",year:"numeric"}),v=({subscription:s})=>e.jsxs("div",{className:"mb-6 rounded-lg bg-[#161616] p-6",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-[#eaeaea]",children:s.name.value}),e.jsx("button",{onClick:()=>f(s),className:"rounded-md bg-[#d32f2f] px-4 py-2 text-sm text-white",children:"Cancel Subscription"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]",children:e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12 2H4C2.89543 2 2 2.89543 2 4V12C2 13.1046 2.89543 14 4 14H12C13.1046 14 14 13.1046 14 12V4C14 2.89543 13.1046 2 12 2Z",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M8 4V8L10.5 10.5",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:"Start Date"}),e.jsx("p",{className:"text-sm text-[#eaeaea]",children:m(s.start_date.value)})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]",children:e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M4 8L14 8",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10 4L14 8L10 12",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:"Plan Type"}),e.jsx("p",{className:"text-sm text-[#eaeaea]",children:s.plan_type.value})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]",children:e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12 2H4C2.89543 2 2 2.89543 2 4V12C2 13.1046 2.89543 14 4 14H12C13.1046 14 14 13.1046 14 12V4C14 2.89543 13.1046 2 12 2Z",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M11 6H5",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M11 10H5",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:"Next Billing Date"}),e.jsx("p",{className:"text-sm text-[#eaeaea]",children:m(s.next_billing_date.value)})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]",children:e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M8 4.5V8H10.5",stroke:"#2E7D32",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:"Billing Cycle"}),e.jsx("p",{className:"text-sm text-[#eaeaea]",children:s.billing_cycle.value})]})]})]})]}),w=()=>e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",children:e.jsxs("div",{className:"w-full max-w-md rounded-lg bg-[#242424] p-6",children:[e.jsx("h3",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Cancel Subscription"}),e.jsxs("p",{className:"mb-6 text-sm text-[#b5b5b5]",children:["Are you sure you want to cancel your ",l==null?void 0:l.name.value," subscription? This action cannot be undone."]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>c(!1),className:"rounded-lg border border-[#363636] bg-transparent px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#363636]/30",children:"No, Keep Subscription"}),e.jsx("button",{onClick:k,className:"rounded-lg bg-[#d32f2f] px-4 py-2 text-sm text-white hover:bg-[#d32f2f]/90",children:"Yes, Cancel"})]})]})});return j?e.jsx("div",{className:"p-4 text-[#eaeaea]",children:"Loading..."}):e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4 md:p-6",children:[d&&e.jsx(C,{message:d,type:"error"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-[#eaeaea]",children:"Manage Your Plan"}),e.jsx("p",{className:"text-[#b5b5b5]",children:"View and manage your subscription details below"})]}),h.map(s=>e.jsx(v,{subscription:s},s.id.value)),e.jsx("div",{className:"mt-8 text-center text-sm text-[#b5b5b5]",children:"Need help? Contact our support team"}),b&&e.jsx(w,{})]})};export{R as default};
