import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as j,r}from"./vendor-1c28ea83.js";import{M as d}from"./index-bdd93324.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const V=()=>{j();const[a,o]=r.useState({email_notifications:!0,push_notifications:!1,two_factor_auth:!1,newsletter:!0,language:"en",timezone:"UTC"}),[p,b]=r.useState(!0),[m,i]=r.useState(""),[u,x]=r.useState("");r.useEffect(()=>{g()},[]);const g=async()=>{try{const t=await new d().GetUserSettings();t.error?i(t.message):o({email_notifications:t.email_notifications.value,push_notifications:t.push_notifications.value,two_factor_auth:t.two_factor_auth.value,newsletter:t.newsletter.value,language:t.language.value,timezone:t.timezone.value})}catch(s){i(s.message)}finally{b(!1)}},c=async s=>{try{const n=await new d().UpdateUserSettings({[s]:{value:!a[s]}});n.error?i(n.message):(o(l=>({...l,[s]:!l[s]})),x("Settings updated successfully"))}catch(t){i(t.message)}},h=async(s,t)=>{try{const l=await new d().UpdateUserSettings({[s]:{value:t}});l.error?i(l.message):(o(f=>({...f,[s]:t})),x("Settings updated successfully"))}catch(n){i(n.message)}};return p?e.jsx("div",{className:"p-4 text-[#eaeaea]",children:"Loading..."}):e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4 md:p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:"Settings"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Manage your account settings"})]}),m&&e.jsx("div",{className:"mb-4 rounded-lg bg-red-100 p-4 text-red-700",children:m}),u&&e.jsx("div",{className:"mb-4 rounded-lg bg-green-100 p-4 text-green-700",children:u}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Notifications"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea]",children:"Email Notifications"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Receive email updates"})]}),e.jsx("button",{onClick:()=>c("email_notifications"),className:`relative h-6 w-11 rounded-full ${a.email_notifications?"bg-[#2e7d32]":"bg-[#363636]"}`,children:e.jsx("span",{className:`absolute left-1 top-1 h-4 w-4 transform rounded-full bg-white transition ${a.email_notifications?"translate-x-5":"translate-x-0"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea]",children:"Push Notifications"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Receive push notifications"})]}),e.jsx("button",{onClick:()=>c("push_notifications"),className:`relative h-6 w-11 rounded-full ${a.push_notifications?"bg-[#2e7d32]":"bg-[#363636]"}`,children:e.jsx("span",{className:`absolute left-1 top-1 h-4 w-4 transform rounded-full bg-white transition ${a.push_notifications?"translate-x-5":"translate-x-0"}`})})]})]})]}),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Security"}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea]",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Add an extra layer of security"})]}),e.jsx("button",{onClick:()=>c("two_factor_auth"),className:`relative h-6 w-11 rounded-full ${a.two_factor_auth?"bg-[#2e7d32]":"bg-[#363636]"}`,children:e.jsx("span",{className:`absolute left-1 top-1 h-4 w-4 transform rounded-full bg-white transition ${a.two_factor_auth?"translate-x-5":"translate-x-0"}`})})]})})]}),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Preferences"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-[#b5b5b5]",children:"Language"}),e.jsxs("select",{value:a.language,onChange:s=>h("language",s.target.value),className:"mt-1 block w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-2.5 text-[#eaeaea]",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",children:"Spanish"}),e.jsx("option",{value:"fr",children:"French"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-[#b5b5b5]",children:"Timezone"}),e.jsxs("select",{value:a.timezone,onChange:s=>h("timezone",s.target.value),className:"mt-1 block w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-2.5 text-[#eaeaea]",children:[e.jsx("option",{value:"UTC",children:"UTC"}),e.jsx("option",{value:"EST",children:"EST"}),e.jsx("option",{value:"PST",children:"PST"})]})]})]})]})]})]})};export{V as default};
