import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const o=({className:t="",fill:i="white",stroke:s="#D4D4D8",onClick:r=()=>{}})=>e.jsxs("svg",{className:`${t}`,onClick:r,width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsxs("g",{"clip-path":"url(#clip0_4045_39235)",children:[e.jsxs("g",{filter:"url(#filter0_di_4045_39235)",children:[e.jsx("path",{d:"M30 40H10C6.68629 40 4 37.3137 4 34V6C4 2.68629 6.68629 0 10 0H20.5147C22.106 0 23.6321 0.632142 24.7574 1.75736L34.2426 11.2426C35.3679 12.3679 36 13.894 36 15.4853V34C36 37.3137 33.3137 40 30 40Z",fill:i}),e.jsx("path",{d:"M30 39.25H10C7.10051 39.25 4.75 36.8995 4.75 34V6C4.75 3.10051 7.10051 0.75 10 0.75H20.5147C21.9071 0.75 23.2425 1.30312 24.227 2.28769L33.7123 11.773C34.6969 12.7575 35.25 14.0929 35.25 15.4853V34C35.25 36.8995 32.8995 39.25 30 39.25Z",stroke:s,strokeWidth:"1.5"})]}),e.jsx("path",{d:"M23 1V9C23 11.2091 24.7909 13 27 13H35",stroke:s,strokeWidth:"1.5"}),e.jsxs("g",{filter:"url(#filter1_i_4045_39235)",children:[e.jsx("mask",{id:"path-4-inside-1_4045_39235",fill:i,children:e.jsx("path",{d:"M0 22C0 19.7909 1.79086 18 4 18H24C26.2091 18 28 19.7909 28 22V30C28 32.2091 26.2091 34 24 34H4C1.79086 34 0 32.2091 0 30V22Z"})}),e.jsx("path",{d:"M0 22C0 19.7909 1.79086 18 4 18H24C26.2091 18 28 19.7909 28 22V30C28 32.2091 26.2091 34 24 34H4C1.79086 34 0 32.2091 0 30V22Z",fill:"#DF1C41"}),e.jsx("path",{d:"M-1 22C-1 19.2386 1.23858 17 4 17H24C26.7614 17 29 19.2386 29 22H27C27 20.3431 25.6569 19 24 19H4C2.34315 19 1 20.3431 1 22H-1ZM28 34H0H28ZM4 34C1.23858 34 -1 31.7614 -1 29V22C-1 19.2386 1.23858 17 4 17V19C2.34315 19 1 20.3431 1 22V30C1 32.2091 2.34315 34 4 34ZM24 17C26.7614 17 29 19.2386 29 22V29C29 31.7614 26.7614 34 24 34C25.6569 34 27 32.2091 27 30V22C27 20.3431 25.6569 19 24 19V17Z",fill:"black",fillOpacity:"0.08",mask:"url(#path-4-inside-1_4045_39235)"}),e.jsx("path",{d:"M3.78906 30V22H6.78906C7.40365 22 7.91927 22.1146 8.33594 22.3437C8.75521 22.5729 9.07161 22.888 9.28516 23.2891C9.5013 23.6875 9.60938 24.1406 9.60938 24.6484C9.60938 25.1615 9.5013 25.6172 9.28516 26.0156C9.06901 26.4141 8.75 26.7279 8.32812 26.957C7.90625 27.1836 7.38672 27.2969 6.76953 27.2969H4.78125V26.1055H6.57422C6.93359 26.1055 7.22786 26.043 7.45703 25.918C7.6862 25.793 7.85547 25.6211 7.96484 25.4023C8.07682 25.1836 8.13281 24.9323 8.13281 24.6484C8.13281 24.3646 8.07682 24.1146 7.96484 23.8984C7.85547 23.6823 7.6849 23.5143 7.45313 23.3945C7.22396 23.2721 6.92839 23.2109 6.56641 23.2109H5.23828V30H3.78906ZM13.7991 30H11.0882V22H13.8538C14.6481 22 15.3304 22.1602 15.9007 22.4805C16.4736 22.7982 16.9137 23.2552 17.221 23.8516C17.5283 24.4479 17.6819 25.1615 17.6819 25.9922C17.6819 26.8255 17.527 27.5417 17.2171 28.1406C16.9098 28.7396 16.4658 29.1992 15.885 29.5195C15.3069 29.8398 14.6116 30 13.7991 30ZM12.5374 28.7461H13.7288C14.2861 28.7461 14.7509 28.6445 15.1233 28.4414C15.4957 28.2357 15.7757 27.9297 15.9632 27.5234C16.1507 27.1146 16.2444 26.6042 16.2444 25.9922C16.2444 25.3802 16.1507 24.8724 15.9632 24.4687C15.7757 24.0625 15.4983 23.7591 15.1311 23.5586C14.7665 23.3555 14.3134 23.2539 13.7718 23.2539H12.5374V28.7461ZM19.2789 30V22H24.4039V23.2148H20.7281V25.3867H24.0523V26.6016H20.7281V30H19.2789Z",fill:i})]})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_di_4045_39235",x:"2",y:"-4",width:"36",height:"47",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[e.jsx("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"1"}),e.jsx("feGaussianBlur",{stdDeviation:"1"}),e.jsx("feComposite",{in2:"hardAlpha",operator:"out"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.894118 0 0 0 0 0.898039 0 0 0 0 0.905882 0 0 0 0.24 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_4045_39235"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_4045_39235",result:"shape"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"-4"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feComposite",{in2:"hardAlpha",operator:"arithmetic",k2:"-1",k3:"1"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.813571 0 0 0 0 0.82 0 0 0 0 0.826429 0 0 0 0.32 0"}),e.jsx("feBlend",{mode:"normal",in2:"shape",result:"effect2_innerShadow_4045_39235"})]}),e.jsxs("filter",{id:"filter1_i_4045_39235",x:"0",y:"18",width:"28",height:"20",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[e.jsx("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"4"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feComposite",{in2:"hardAlpha",operator:"arithmetic",k2:"-1",k3:"1"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.08 0"}),e.jsx("feBlend",{mode:"normal",in2:"shape",result:"effect1_innerShadow_4045_39235"})]}),e.jsx("clipPath",{id:"clip0_4045_39235",children:e.jsx("rect",{width:"40",height:"40",fill:i})})]})]});export{o as default};
