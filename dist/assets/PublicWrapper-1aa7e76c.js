import{j as e}from"./@react-google-maps/api-211df1ae.js";import{L as s}from"./vendor-1c28ea83.js";const l=({children:a})=>e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e]",children:[e.jsx("header",{className:"fixed top-0 z-10 w-full border-b border-[#363636] bg-[#161616]",children:e.jsxs("div",{className:"mx-auto flex h-16 max-w-7xl items-center justify-between px-4",children:[e.jsxs(s,{to:"/",className:"flex items-center gap-2",children:[e.jsx("img",{src:"/logo.svg",alt:"Logo",className:"h-8"}),e.jsx("span",{className:"text-xl font-bold text-[#eaeaea]",children:"<PERSON><PERSON><PERSON>"})]}),e.jsxs("nav",{className:"flex items-center gap-6",children:[e.jsx(s,{to:"/member/login",className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:"Login"}),e.jsx(s,{to:"/member/signup",className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea]",children:"Sign Up"})]})]})}),e.jsx("main",{className:"pt-16",children:a})]});export{l as PublicWrapper,l as default};
