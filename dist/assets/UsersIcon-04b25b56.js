import{j as C}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const i=({className:s="",fill:t="none",stroke:o="black",onClick:e=()=>{}})=>C.jsx("svg",{className:`${s}`,onClick:e,width:"22",height:"20",viewBox:"0 0 22 20",fill:t,xmlns:"http://www.w3.org/2000/svg",children:C.jsx("path",{d:"M17 13.8369C18.4559 14.5683 19.7041 15.742 20.6152 17.2096C20.7957 17.5003 20.8859 17.6456 20.9171 17.8468C20.9805 18.2558 20.7008 18.7585 20.3199 18.9204C20.1325 19 19.9217 19 19.5 19M15 9.53224C16.4817 8.79589 17.5 7.26686 17.5 5.5C17.5 3.73314 16.4817 2.20411 15 1.46776M13 5.5C13 7.98528 10.9853 10 8.5 10C6.01472 10 4 7.98528 4 5.5C4 3.01472 6.01472 1 8.5 1C10.9853 1 13 3.01472 13 5.5ZM1.55923 16.9383C3.15354 14.5446 5.66937 13 8.5 13C11.3306 13 13.8465 14.5446 15.4408 16.9383C15.79 17.4628 15.9647 17.725 15.9446 18.0599C15.9289 18.3207 15.758 18.64 15.5496 18.7976C15.2819 19 14.9138 19 14.1776 19H2.82236C2.08617 19 1.71808 19 1.45044 18.7976C1.24205 18.64 1.07109 18.3207 1.05543 18.0599C1.03533 17.725 1.20996 17.4628 1.55923 16.9383Z",stroke:o,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})});export{i as default};
