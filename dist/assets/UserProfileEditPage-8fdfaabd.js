import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as l}from"./vendor-1c28ea83.js";import{u as p}from"./react-hook-form-eec8b32f.js";import{M as t}from"./MkdInput-8c5a964a.js";import{P as d}from"./index-3b048cac.js";import{U as n}from"./index-468ef424.js";import{C as c,T as x}from"./index-3bf596a0.js";import{I as f}from"./index-632d14e3.js";import"./index-bdd93324.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const J=()=>{const[a,e]=l.useState({showModal:!1,modal:""}),{register:o,handleSubmit:r}=p(),i=m=>{console.log(m)};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs(c,{children:[s.jsx(x,{children:"Edit Profile"}),s.jsxs("form",{onSubmit:r(i),className:"space-y-8",children:[s.jsx("div",{className:"mx-auto max-w-xs",children:s.jsx(d,{})}),s.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[s.jsx(t,{label:"First Name",...o("first_name")}),s.jsx(t,{label:"Last Name",...o("last_name")}),s.jsx(t,{label:"Email",type:"email",...o("email")}),s.jsx(t,{label:"Phone",...o("phone")})]}),s.jsxs("div",{className:"flex justify-end gap-4",children:[s.jsx("button",{type:"button",className:"text-[#7dd87d]",onClick:()=>e({showModal:!0,modal:"password"}),children:"Change Password"}),s.jsx(f,{type:"submit",className:"rounded-lg bg-[#2e7d32] px-6 py-3 text-[#eaeaea]",children:"Save Changes"})]})]})]}),s.jsx(n,{isOpen:a.showModal&&a.modal==="password",onClose:()=>e({showModal:!1,modal:""})})]})};export{J as default};
