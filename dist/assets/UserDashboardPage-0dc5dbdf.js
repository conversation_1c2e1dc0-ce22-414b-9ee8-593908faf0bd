import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as ae,r,R as re,L as le}from"./vendor-1c28ea83.js";import{G as ne,M as C,s as v}from"./index-bdd93324.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const y=i=>{const n=new Date(i),d=n.toLocaleString("default",{month:"short"}),N=n.getDate();return`${n.toLocaleString("default",{weekday:"short"})}, ${d} ${N}, ${n.toLocaleString("default",{hour:"numeric",minute:"numeric",hour12:!0})}`},P=({isOpen:i,onClose:n,children:d})=>i?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"relative w-[500px] rounded-lg bg-[#242424] p-6",children:[e.jsx("button",{onClick:n,className:"absolute right-4 top-4 text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),d]})}):null,Me=()=>{const i=ae(),{dispatch:n}=r.useContext(ne),[d,N]=r.useState([]),[b,f]=r.useState([]),[g,w]=r.useState([]),[L,Q]=r.useState([]),[S,A]=r.useState(!1),[c,V]=r.useState(""),[oe,D]=r.useState(""),[M,u]=r.useState(!1),[j,p]=r.useState(""),[Y,_]=r.useState(!1),[k,z]=r.useState(null),[K,E]=r.useState(!1),[x,Z]=r.useState(null);r.useEffect(()=>{J()},[]),console.log("notes",g);const J=async()=>{try{const s=new C,[t,a]=await Promise.all([s.GetCommunityUpdates(),s.GetDashboardStats()]);if(!t.error&&!a.error){const{notes:o=[],recentActivity:l=[]}=a.data;N(t.updates),f(t.updates),console.log("notesRes",o),w(o),Q(l)}else throw new Error(t.message||a.message)}catch(s){D(s.message),v(n,s.message,5e3,"error")}finally{}},X=(s,t)=>{let a;return function(...l){const h=()=>{clearTimeout(a),s(...l)};clearTimeout(a),a=setTimeout(h,t)}},q=s=>(console.log(s),s.split(" ").map(t=>t[0]).join("").toUpperCase().slice(0,2)),R=({user:s})=>{var t;return(t=s.author_photo)!=null&&t.value?e.jsx("img",{src:s.author_photo.value,alt:s.author.value,className:"h-8 w-8 rounded-full object-cover"}):e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:q(s.author.value)})},ee=s=>{if(!s.trim()){A(!1),f(d);return}A(!0);const t=d.filter(a=>{var l,h,I,O,U,$,F,G,H;const o=s.toLowerCase();return!!((h=(l=a.title)==null?void 0:l.value)!=null&&h.toLowerCase().includes(o)||(O=(I=a.author)==null?void 0:I.value)!=null&&O.toLowerCase().includes(o)||($=(U=a.type)==null?void 0:U.value)!=null&&$.toLowerCase().includes(o)||(H=(G=(F=a.event_details)==null?void 0:F.value)==null?void 0:G.content)!=null&&H.toLowerCase().includes(o))});f(t)},se=re.useCallback(X(s=>ee(s),300),[d]),te=s=>{const t=s.target.value;V(t),se(t)},m=(s,t)=>{if(!t.trim())return s;console.log(s,t);const a=new RegExp(`(${t})`,"gi");return((s==null?void 0:s.split(a))||[]).map((l,h)=>a.test(l)?e.jsx("span",{className:"bg-[#2e7d32]/20 text-[#7dd87d]",children:l},h):l)},T=async()=>{if(j.trim())try{const t=await new C().CreateNote({content:j});t.error||(w(a=>[t.data,...a]),p(""),u(!1))}catch(s){D(s.message),v(n,s.message,5e3,"error")}},B=async s=>{try{const a=await new C().callRawAPI(`/v1/api/dealmaker/user/notes/${s}`,{},"DELETE");if(!a.error)w(o=>o.filter(l=>l.id.value!==s)),v(n,"Note deleted successfully!",5e3,"success");else throw new Error(a.message||"Failed to delete note")}catch(t){console.error("Error deleting note:",t),v(n,t.message||"Failed to delete note",5e3,"error")}},W=s=>{s.type.value.toLowerCase()==="note"?(z(s.event_details.value),_(!0)):s.type.value.toLowerCase()==="task"?(Z(s.event_details.value),E(!0)):s.type.value.toLowerCase().includes("referral")?i("/member/referrals",{state:{activeTab:"referrals-feed",referralId:s.reference_id.value}}):s.type.value.toLowerCase().includes("community")&&i("/member/communities",{state:{activeTab:"referrals-feed",referralId:s.reference_id.value}})};return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4 w-full",children:[e.jsxs(P,{isOpen:Y,onClose:()=>_(!1),children:[e.jsx("div",{className:"mb-4",children:e.jsx("h3",{className:"text-lg font-semibold text-[#eaeaea]",children:"Note Details"})}),e.jsx("div",{className:"text-[#eaeaea]",children:k==null?void 0:k.content})]}),e.jsxs(P,{isOpen:K,onClose:()=>E(!1),children:[e.jsx("div",{className:"mb-4",children:e.jsx("h3",{className:"text-lg font-semibold text-[#eaeaea]",children:"Task Details"})}),e.jsxs("div",{className:"text-[#eaeaea]",children:[e.jsx("p",{className:"mb-2",children:x==null?void 0:x.content}),(x==null?void 0:x.due_date)&&e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:["Due: ",y(x.due_date)]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:S?e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:"My Feed"}):e.jsxs("div",{children:[e.jsx("p",{className:"text-[#EAEAEA] text-[24px] font-bold",children:"Welcome to RainmakerOS"}),e.jsx("p",{className:"text-[16px] text-[#B5B5B5]",children:"Your default community is Rain Maker LLC"}),e.jsxs(le,{to:"/member/communities",children:[" ",e.jsx("button",{className:"text-[#7DD87D] text-[16px] underline",children:"Click here to view your community"})]})]})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:c,onChange:te,placeholder:"Search...",className:"h-[42px] w-64 border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea] placeholder-[#666]"}),e.jsx("div",{style:{top:"50%",right:"10px",transform:"translateY(-50%)"},className:"absolute right-3 text-white",children:e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M15.7955 15.8111L21 21M18 10.5C18 14.6421 14.6421 18 10.5 18C6.35786 18 3 14.6421 3 10.5C3 6.35786 6.35786 3 10.5 3C14.6421 3 18 6.35786 18 10.5Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsx("button",{onClick:()=>i("/member/referrals/add"),className:"rounded bg-[#2e7d32] h-[42px] px-4 py-2 text-sm text-[#eaeaea]",children:"+ Post Opportunity"})]})]})}),S?e.jsxs("div",{style:{width:"100%"},className:"flex gap-6",children:[e.jsx("div",{style:{width:"65%"},className:"flex-1",children:e.jsxs("div",{className:"mb-6 bg-black rounded p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-bold text-[#eaeaea]",children:"Search Results"}),e.jsx("div",{className:"space-y-4",children:b.length>0?b.map(s=>e.jsx("div",{className:"rounded border border-[#363636] bg-[#242424] p-4",children:e.jsx("div",{className:"mb-2",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(R,{user:s}),e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea] mb-1 text-[16px]",children:m(s.title.value,c)}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[m(s.author.value,c)," • ",y(s.created_at.value)]})]}),e.jsx("div",{className:"flex items-center gap-2 ml-4",children:e.jsx("button",{onClick:()=>W(s),className:"text-[#7dd87d] text-right text-[16px] hover:underline",children:s.type.value.toLowerCase()!=="recommendation"?m(s.type.value.split(" ").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" "),c):""})})]})})]})})},s.id.value)):e.jsx("div",{className:"text-center py-4 text-[#b5b5b5]",children:"No activities found matching your search."})})]})}),e.jsxs("div",{style:{width:"35%"},className:"w-80 space-y-6",children:[e.jsxs("div",{className:"rounded p-4 bg-black",children:[e.jsx("h2",{className:"mb-4 text-xl font-bold text-[#eaeaea]",children:"Recent Activity"}),e.jsx("div",{className:"space-y-4",children:L.map(s=>e.jsxs("div",{className:"border-b border-[#363636] pb-4",children:[e.jsx("p",{className:"text-[16px] text-[#eaeaea]",children:s.title.value}),e.jsx("p",{className:"text-[14px] text-[#b5b5b5]",children:new Date(s.created_at.value).toLocaleString()})]},s.id.value))})]}),e.jsxs("div",{className:"rounded-xl bg-black p-4 mt-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-bold text-[#eaeaea]",children:"Notes"}),e.jsx("button",{onClick:()=>u(!0),className:"flex h-6 w-6 items-center justify-center  text-[#7dd87d] text-2xl hover:bg-[#1b5e20]",children:e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]}),M&&e.jsxs("div",{className:"mb-4",children:[e.jsx("textarea",{value:j,onChange:s=>p(s.target.value),placeholder:"Write your note...",className:"mb-2 w-full rounded border border-[#363636] bg-[#242424] p-2 text-sm text-[#eaeaea] placeholder-[#666]",rows:3}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>{u(!1),p("")},className:"rounded px-3 py-1 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:T,className:"rounded bg-[#2e7d32] px-3 py-1 text-sm text-white hover:bg-[#1b5e20]",children:"Add Note"})]})]}),e.jsx("div",{className:"space-y-4",children:g.map(s=>e.jsx("div",{className:"border-b border-[#363636] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[16px] text-[#eaeaea]",children:s.content.value}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:new Date(s.created_at.value).toLocaleString()})]}),e.jsx("button",{onClick:()=>B(s.id.value),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},s.id.value))})]})]})]}):e.jsxs("div",{style:{width:"100%"},className:"flex gap-6",children:[e.jsxs("div",{style:{width:"65%"},className:"flex-1 bg-black rounded p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-bold text-[#eaeaea]",children:"Community Updates"}),e.jsx("div",{className:"space-y-4",children:b.length>0?b.map(s=>e.jsx("div",{className:"rounded border border-[#363636] bg-[#242424] p-4",children:e.jsx("div",{className:"mb-2",children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(R,{user:s}),e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea] mb-1 text-[16px]",children:c?m(s.title.value,c):s.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[c?m(s.author.value,c):s.author.value," • ",y(s.created_at.value)]})]}),e.jsx("div",{className:"flex items-center gap-2 ml-4",children:e.jsx("button",{onClick:()=>W(s),className:"text-[#7dd87d] text-right text-[16px] hover:underline",children:s.type.value.toLowerCase()!=="recommendation"?c?m(s.type.value.split(" ").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" "),c):s.type.value.split(" ").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" "):""})})]})})]})})},s.id.value)):e.jsx("div",{className:"text-center py-4 text-[#b5b5b5]",children:"No activities found matching your search."})})]}),e.jsxs("div",{style:{width:"35%"},className:"space-y-6",children:[e.jsxs("div",{className:"p-4 bg-black rounded-[12px]",children:[e.jsx("h2",{className:"mb-4 text-xl font-bold text-[#eaeaea]",children:"Recent Activity"}),e.jsx("div",{className:"space-y-4",children:L.map(s=>e.jsxs("div",{className:"border-b border-[#363636]  pb-4",children:[e.jsx("p",{className:"text-[#eaeaea] text-[16px]",children:s.title.value}),e.jsx("p",{className:"text-[14px] text-[#b5b5b5]",children:new Date(s.created_at.value).toLocaleString()})]},s.id.value))})]}),e.jsxs("div",{className:"rounded-xl bg-black p-4 mt-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Notes"}),e.jsx("button",{onClick:()=>u(!0),className:"flex h-6 w-6 items-center justify-center  text-[#7dd87d] text-2xl hover:bg-[#1b5e20]",children:e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]}),M&&e.jsxs("div",{className:"mb-4",children:[e.jsx("textarea",{value:j,onChange:s=>p(s.target.value),placeholder:"Write your note...",className:"mb-2 w-full rounded border border-[#363636] bg-[#242424] p-2 text-[16px] text-[#eaeaea] placeholder-[#666]",rows:3}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>{u(!1),p("")},className:"rounded px-3 py-1 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:T,className:"rounded bg-[#2e7d32] px-3 py-1 text-sm text-white hover:bg-[#1b5e20]",children:"Add Note"})]})]}),e.jsx("div",{className:"space-y-4",children:g.map(s=>e.jsx("div",{className:"border-b border-[#363636] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[16px] text-[#eaeaea]",children:s.content.value}),e.jsx("p",{className:"text-[14px] text-[#b5b5b5]",children:new Date(s.created_at.value).toLocaleString()})]}),e.jsx("button",{onClick:()=>B(s.id.value),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},s.id.value))})]})]})]})]})};export{Me as default};
