import{j as C}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const a=({className:L="",stroke:e="#717179",fill:l="none",onClick:s=()=>{}})=>C.jsxs("svg",{className:`${L}`,onClick:s,width:"19",height:"16",viewBox:"0 0 19 16",fill:l,xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.99984 0.5C2.15889 0.5 0.666504 1.99238 0.666504 3.83333C0.666504 5.67428 2.15889 7.16667 3.99984 7.16667C5.00413 7.16667 5.90469 6.72253 6.51582 6.01995L9.32956 7.99999L6.51581 9.98004C5.90468 9.27747 5.00413 8.83333 3.99984 8.83333C2.15889 8.83333 0.666504 10.3257 0.666504 12.1667C0.666504 14.0076 2.15889 15.5 3.99984 15.5C5.84079 15.5 7.33317 14.0076 7.33317 12.1667C7.33317 11.6293 7.20604 11.1217 6.98021 10.6722L18.3315 2.68428L17.9237 2.555C17.5516 2.43706 17.1458 2.49969 16.8266 2.72431L10.0536 7.4905L6.98021 5.32776C7.20604 4.87827 7.33317 4.37065 7.33317 3.83333C7.33317 1.99238 5.84079 0.5 3.99984 0.5ZM1.49984 3.83333C1.49984 2.45262 2.61913 1.33333 3.99984 1.33333C5.38055 1.33333 6.49984 2.45262 6.49984 3.83333C6.49984 5.21405 5.38055 6.33333 3.99984 6.33333C2.61913 6.33333 1.49984 5.21405 1.49984 3.83333ZM1.49984 12.1667C1.49984 10.786 2.61913 9.66667 3.99984 9.66667C5.38055 9.66667 6.49984 10.786 6.49984 12.1667C6.49984 13.5474 5.38055 14.6667 3.99984 14.6667C2.61913 14.6667 1.49984 13.5474 1.49984 12.1667Z",fill:"black"}),C.jsx("path",{d:"M16.8266 13.2757L10.7771 9.01862L11.5011 8.50913L18.3315 13.3157L17.9237 13.445C17.5516 13.5629 17.1458 13.5003 16.8266 13.2757Z",fill:"black"}),C.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.99984 0.5C2.15889 0.5 0.666504 1.99238 0.666504 3.83333C0.666504 5.67428 2.15889 7.16667 3.99984 7.16667C5.00413 7.16667 5.90469 6.72253 6.51582 6.01995L9.32956 7.99999L6.51581 9.98004C5.90468 9.27747 5.00413 8.83333 3.99984 8.83333C2.15889 8.83333 0.666504 10.3257 0.666504 12.1667C0.666504 14.0076 2.15889 15.5 3.99984 15.5C5.84079 15.5 7.33317 14.0076 7.33317 12.1667C7.33317 11.6293 7.20604 11.1217 6.98021 10.6722L18.3315 2.68428L17.9237 2.555C17.5516 2.43706 17.1458 2.49969 16.8266 2.72431L10.0536 7.4905L6.98021 5.32776C7.20604 4.87827 7.33317 4.37065 7.33317 3.83333C7.33317 1.99238 5.84079 0.5 3.99984 0.5ZM1.49984 3.83333C1.49984 2.45262 2.61913 1.33333 3.99984 1.33333C5.38055 1.33333 6.49984 2.45262 6.49984 3.83333C6.49984 5.21405 5.38055 6.33333 3.99984 6.33333C2.61913 6.33333 1.49984 5.21405 1.49984 3.83333ZM1.49984 12.1667C1.49984 10.786 2.61913 9.66667 3.99984 9.66667C5.38055 9.66667 6.49984 10.786 6.49984 12.1667C6.49984 13.5474 5.38055 14.6667 3.99984 14.6667C2.61913 14.6667 1.49984 13.5474 1.49984 12.1667Z",stroke:e,strokeLinecap:"square"}),C.jsx("path",{d:"M16.8266 13.2757L10.7771 9.01862L11.5011 8.50913L18.3315 13.3157L17.9237 13.445C17.5516 13.5629 17.1458 13.5003 16.8266 13.2757Z",stroke:e,strokeLinecap:"square"})]});export{a as default};
