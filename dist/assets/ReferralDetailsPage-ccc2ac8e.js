import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const t=()=>e.jsx("div",{className:"space-y-6 p-4 md:p-6",children:e.jsxs("div",{className:"rounded-xl bg-[#161616] p-6",children:[e.jsxs("div",{className:"mb-8 flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-bold text-[#eaeaea]",children:"My client is looking for a Driver for his company"}),e.jsxs("div",{className:"mt-2 flex items-center gap-4",children:[e.jsx("p",{className:"text-[#b5b5b5]",children:"Posted by <PERSON> - Jan 1, 2025"}),e.jsxs("div",{className:"flex items-center gap-2 text-[#7dd87d]",children:[e.jsx("svg",{className:"h-3.5 w-3.5",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsx("span",{children:"Direct Referral to Tyron"})]})]})]}),e.jsx("span",{className:"rounded-full bg-[#2e7d3233] px-4 py-1.5 text-sm text-[#7dd87d]",children:"Status: Active"})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("button",{className:"flex items-center gap-2 rounded-lg border border-[#7dd87d] bg-[#161616] px-4 py-2 text-[#eaeaea] hover:bg-[#1e1e1e]",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-lg bg-[#d32f2f] px-4 py-2 text-[#eaeaea] hover:bg-[#b71c1c]",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea] hover:bg-[#1b5e20]",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Mark Complete"]})]})]})});export{t as default};
