import{j as o}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{L as t,a7 as l}from"./index-bdd93324.js";import{M as s}from"./index-aed072b8.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const B=({isOpen:r=!1,title:i="change route",onClose:m,onSuccess:a,options:e=[{name:"",route:""}],modalClasses:p={modalDialog:"max-h-[90%] min-h-[12rem] overflow-y-auto !w-full md:!w-[29.0625rem]",modal:"h-full"},customMessage:d=""})=>o.jsx(t,{children:o.jsx(s,{isOpen:r,modalCloseClick:m,title:i,modalHeader:!0,classes:p,children:r&&o.jsx(t,{children:o.jsx(l,{onClose:m,onSuccess:a,options:e})})})});export{B as RouteChangeModal,B as default};
