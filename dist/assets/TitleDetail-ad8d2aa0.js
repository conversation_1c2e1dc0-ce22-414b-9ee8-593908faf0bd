import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const l=({title:r="",data:t=""})=>e.jsxs("div",{className:"relative grow",children:[e.jsx("span",{className:"mb-2 block w-full cursor-pointer text-[.875rem] font-bold",children:r}),e.jsx("div",{className:"flex h-[3rem] w-full items-center rounded-[.625rem] border border-gray-200 bg-gray-200 p-[.625rem] px-3 py-2 font-inter leading-tight text-black shadow focus:outline-none focus:ring-0",children:t})]});export{l as TitleDetail,l as default};
