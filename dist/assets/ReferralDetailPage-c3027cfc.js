import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{U as s}from"./UserCircleIcon-1279e158.js";const r=()=>e.jsx("div",{className:"space-y-6 p-4 md:p-6",children:e.jsxs("div",{className:"rounded-xl bg-[#252525] p-6",children:[e.jsx("div",{className:"mb-6 flex items-start justify-between",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"h-12 w-12 rounded-full bg-cover bg-center"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold text-[#eaeaea]",children:"My client is looking for a Driver for his company"}),e.jsxs("div",{className:"mt-1 flex items-center gap-2 text-sm",children:[e.jsx("span",{className:"text-[#b5b5b5]",children:"Posted by John Doe"}),e.jsx("span",{className:"text-[#a0a0a0]",children:"• 2 hours ago"})]})]})]})}),e.jsx("div",{className:"mb-6 space-y-4"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("button",{className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-6 py-2 text-[#eaeaea]",children:[e.jsx(s,{className:"h-5 w-5"}),"Refer"]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-6 py-2 text-[#eaeaea]",children:[e.jsx(s,{className:"h-5 w-5"}),"Chat"]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-6 py-2 text-[#eaeaea]",children:[e.jsx(s,{className:"h-5 w-5"}),"Repost"]})]})]})});export{r as default};
