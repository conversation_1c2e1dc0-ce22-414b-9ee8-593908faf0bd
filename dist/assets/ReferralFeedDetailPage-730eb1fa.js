import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{M as t}from"./MkdInput-8c5a964a.js";import{U as r}from"./UserCircleIcon-1279e158.js";import"./index-bdd93324.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const U=()=>e.jsxs("div",{className:"space-y-6 p-4 md:p-6",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-[#eaeaea]",children:"Manaknight Community"}),e.jsx("p",{className:"text-[#b5b5b5]",children:"Community Admin Panel"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(t,{className:"h-[42px] w-64 rounded-lg border border-[#363636] bg-[#161616] px-4",placeholder:"Search posts..."}),e.jsxs("button",{className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea]",children:[e.jsx(r,{className:"h-5 w-5"}),"Create Post"]})]})]}),e.jsx("div",{className:"rounded-xl bg-[#252525] p-6"})]});export{U as default};
