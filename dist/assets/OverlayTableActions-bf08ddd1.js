import{j as s}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{L as m,ak as N,aj as g,a as b}from"./index-bdd93324.js";import{A as j}from"./index-e2604cb4.js";import{o as i}from"./config-c6ac2907.js";import{M as C}from"./index-dbfe2d0c.js";import{D as v}from"./index-395d2933.js";import{p as F}from"./MkdListTableBindOperations-46ca2ffa.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-1b5612ec.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";const t=({actions:r,selectedItems:e,currentTableData:d})=>{var h;return s.jsxs("div",{className:"fixed inset-x-0 bottom-5 z-[999999] m-auto flex !h-[3.25rem] !max-h-[3.25rem] w-fit items-center justify-start gap-2 rounded-[.875rem] bg-black px-[.75rem] pb-[.5rem] pt-[.5rem]",children:[s.jsxs("div",{className:"font-inter text-white",children:["Selected: ",e.length]}),(h=Object.keys(r).filter(l=>{var p,x,u,f;return((p=r[l])==null?void 0:p.show)&&((x=r[l])==null?void 0:x.locations)&&((f=(u=r[l])==null?void 0:u.locations)==null?void 0:f.includes("overlay"))}))!=null&&h.length?s.jsx(s.Fragment,{children:Object.keys(r).filter(l=>{var p,x,u,f;return((p=r[l])==null?void 0:p.show)&&((x=r[l])==null?void 0:x.locations)&&((f=(u=r[l])==null?void 0:u.locations)==null?void 0:f.includes("overlay"))}).map((l,p)=>{var x,u,f;if((x=r[l])!=null&&x.type&&[i.DROPDOWN].includes((u=r[l])==null?void 0:u.type))return s.jsx(R,{actionKey:l,action:r[l],selectedItems:e,currentTableData:d},p);if(!((f=r[l])!=null&&f.type))return s.jsx(A,{actionKey:l,action:r[l],selectedItems:e,currentTableData:d},p)}).filter(Boolean)}):null]})},R=({action:r,actionKey:e,selectedItems:d,currentTableData:h})=>(h.filter(l=>d.includes(l==null?void 0:l.id)),s.jsx(m,{children:s.jsx(C,{display:s.jsxs("span",{className:"hover:text[#262626] relative flex h-[3rem] w-full cursor-pointer items-center justify-between gap-2 overflow-hidden rounded-[.625rem] border !border-white-200 border-primary !bg-white-100 bg-primary px-2  py-2 font-inter text-sm font-medium capitalize  leading-loose tracking-wide  text-white hover:bg-[#F4F4F4]",children:[s.jsxs("span",{className:"flex grow items-center justify-start gap-3 text-white",children:[r==null?void 0:r.icon,(r==null?void 0:r.children)??e]}),s.jsx(N,{className:"-rotate-90"})]}),zIndex:999,className:"w-full",tooltipClasses:"!rounded-[.5rem] !text-white w-full !min-w-[11rem] !px-0 !right-[3.25rem] !border",place:"top-start",backgroundColor:"#18181B",children:r!=null&&r.options&&Object.keys(r==null?void 0:r.options).length?Object.keys(r==null?void 0:r.options).map((l,p)=>s.jsx(O,{action:r==null?void 0:r.options[l],selectedItems:d,currentTableData:h},p)):null})})),O=({action:r,selectedItems:e,currentTableData:d})=>{var l;const h=d.filter(p=>e.includes(p==null?void 0:p.id));if(r!=null&&r.bind)switch((l=r==null?void 0:r.bind)==null?void 0:l.action){case"hide":if(!F(r,h))return s.jsx(m,{children:s.jsx(v,{name:(r==null?void 0:r.children)??key,className:"!text-white hover:!bg-white-100 ",icon:r==null?void 0:r.icon,onClick:()=>{r!=null&&r.action&&(r==null||r.action(e))}})})}if(!(r!=null&&r.bind))return s.jsx(m,{children:s.jsx(v,{name:(r==null?void 0:r.children)??key,className:"!text-white hover:!bg-white-100 ",icon:r==null?void 0:r.icon,onClick:()=>{r!=null&&r.action&&(r==null||r.action(e))}})})},A=({selectedItems:r,action:e,actionKey:d,currentTableData:h})=>{const l=h.filter(p=>r.includes(p==null?void 0:p.id));if(r&&(r==null?void 0:r.length)===1&&!(e!=null&&e.multiple))return s.jsx(w,{action:e,actionKey:d,selectedItems:r,rows:l});if(r&&(r==null?void 0:r.length)>=1&&(e!=null&&e.multiple))if(e!=null&&e.multipleFrom){if(!!(e!=null&&e.multipleFrom&&(r==null?void 0:r.length)>=(e==null?void 0:e.multipleFrom)))return s.jsx(w,{action:e,actionKey:d,selectedItems:r,rows:l})}else return s.jsx(w,{action:e,actionKey:d,selectedItems:r,rows:l})},w=({action:r,actionKey:e,selectedItems:d,rows:h})=>{var l;if(r!=null&&r.bind)switch((l=r==null?void 0:r.bind)==null?void 0:l.action){case"hide":if(!F(r,h))return s.jsx(m,{children:s.jsxs(j,{showPlus:!1,loading:(r==null?void 0:r.loading)??!1,disabled:(r==null?void 0:r.disabled)??!1,icon:(r==null?void 0:r.icon)??null,className:`flex cursor-pointer gap-2 !border-white-200 !bg-white-100 px-2 py-2 text-lg font-medium  leading-loose tracking-wide !text-white ${e==="view"?"text-blue-500":e==="delete"?"!text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{r!=null&&r.action&&r.action(d)},children:[e==="delete"?s.jsx(g,{}):null,r.children?r.children:s.jsx(s.Fragment,{children:b(e==="delete"?"Remove":e,{casetype:"capitalize",separator:" "})})]})})}if(!(r!=null&&r.bind))return s.jsx(m,{children:s.jsxs(j,{showPlus:!1,loading:(r==null?void 0:r.loading)??!1,disabled:(r==null?void 0:r.disabled)??!1,icon:(r==null?void 0:r.icon)??null,className:`flex cursor-pointer gap-2 !border-white-200 !bg-white-100 px-2 py-2 text-lg font-medium  leading-loose tracking-wide !text-white ${e==="view"?"text-blue-500":e==="delete"?"!text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{r!=null&&r.action&&r.action(d)},children:[e==="delete"?s.jsx(g,{}):null,r.children?r.children:s.jsx(s.Fragment,{children:b(e==="delete"?"Remove":e,{casetype:"capitalize",separator:" "})})]})})};export{t as default};
